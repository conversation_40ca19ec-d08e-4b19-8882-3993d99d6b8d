# 角色菜单权限功能文档

## 功能概述

本功能实现了基于角色的菜单权限控制系统，允许为不同角色分配不同的菜单访问权限和操作权限。

## 数据库结构

### 核心表

1. **roles** - 角色表
2. **menus** - 菜单表  
3. **menu_permissions** - 菜单权限表
4. **role_menu_permissions** - 角色菜单权限关联表
5. **user_roles** - 用户角色关联表

### 权限模型

- **菜单访问权限**: 用户是否能看到某个菜单项
- **菜单操作权限**: 用户是否能执行菜单下的特定操作（如新增、编辑、删除等）

## API接口

### 1. 获取角色的菜单权限

```http
GET /api/admin/roles/{role}/menu-permissions
```

**响应示例:**
```json
{
  "role": {
    "id": 1,
    "name": "admin",
    "description": "系统管理员"
  },
  "permissions": [
    {
      "menu_id": 1,
      "menu_name": "User",
      "menu_title": "用户管理",
      "has_menu_access": true,
      "permissions": [
        {
          "id": 1,
          "title": "新增",
          "auth_mark": "add"
        }
      ]
    }
  ]
}
```

### 2. 获取菜单列表（用于权限分配）

```http
GET /api/admin/menus/for-assignment
```

### 3. 为角色分配菜单权限

```http
POST /api/admin/roles/{role}/menu-permissions/assign
```

**请求体:**
```json
{
  "permissions": [
    {
      "menu_id": 1,
      "menu_permission_id": null  // null表示只有菜单访问权限
    },
    {
      "menu_id": 1,
      "menu_permission_id": 1     // 具体的操作权限
    }
  ]
}
```

### 4. 同步角色的菜单权限（覆盖式）

```http
POST /api/admin/roles/{role}/menu-permissions/sync
```

### 5. 移除角色的菜单权限

```http
DELETE /api/admin/roles/{role}/menu-permissions/remove
```

## 权限验证

### 中间件使用

```php
// 检查菜单访问权限
Route::middleware('menu.permission:User')->group(function () {
    // 用户管理相关路由
});

// 检查具体操作权限
Route::middleware('menu.permission:User,add')->group(function () {
    // 需要新增权限的路由
});
```

### 在控制器中检查权限

```php
// 检查菜单访问权限
if (!$user->hasMenuAccess($menuId)) {
    return response()->json(['message' => '没有访问权限'], 403);
}

// 检查具体操作权限
if (!$user->hasMenuPermission($menuId, $permissionId)) {
    return response()->json(['message' => '没有操作权限'], 403);
}

// 通过权限标识检查
if (!$user->hasMenuPermissionByMark('User', 'add')) {
    return response()->json(['message' => '没有新增权限'], 403);
}
```

## 前端集成

### 获取用户菜单

前端调用 `/api/admin/menus` 接口时，系统会自动根据用户的角色权限过滤菜单：

- 管理员角色：返回所有菜单
- 其他角色：只返回有权限访问的菜单

### 按钮权限控制

前端可以根据菜单的 `meta.authList` 来控制按钮显示：

```javascript
// 检查是否有新增权限
const hasAddPermission = menu.meta.authList.some(auth => auth.authMark === 'add');
```

## 默认角色

系统预设了两个默认角色：

1. **admin** - 系统管理员，拥有所有权限
2. **user** - 普通用户，只有基础菜单的访问权限

## 使用示例

### 1. 创建新角色并分配权限

```php
// 创建角色
$role = Role::create([
    'name' => 'editor',
    'description' => '编辑员'
]);

// 分配权限
$permissions = [
    ['menu_id' => 1, 'menu_permission_id' => null], // 菜单访问权限
    ['menu_id' => 1, 'menu_permission_id' => 1],    // 新增权限
    ['menu_id' => 1, 'menu_permission_id' => 2],    // 编辑权限
];

$roleService->assignMenuPermissions($role, $permissions);
```

### 2. 为用户分配角色

```php
$user = User::find(1);
$user->roles()->attach($roleId);
```

### 3. 检查用户权限

```php
$user = auth()->user();

// 检查是否有用户管理菜单的访问权限
if ($user->hasMenuAccess($userMenuId)) {
    // 显示用户管理菜单
}

// 检查是否有新增用户的权限
if ($user->hasMenuPermissionByMark('User', 'add')) {
    // 显示新增按钮
}
```

## 注意事项

1. 权限检查是基于角色的，用户可以拥有多个角色
2. 菜单权限分为访问权限和操作权限两个层级
3. 管理员角色默认拥有所有权限
4. 权限变更后需要用户重新登录才能生效（或清除缓存）
5. 删除角色时会自动清理相关的权限数据
