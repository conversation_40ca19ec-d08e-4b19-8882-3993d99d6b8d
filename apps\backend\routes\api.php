<?php

use App\Http\Controllers\Admin\AssetController;
use App\Http\Controllers\Admin\AttachmentController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\DictionaryController;
use App\Http\Controllers\Admin\EntityContactController;
use App\Http\Controllers\Admin\EntityController;
use App\Http\Controllers\Admin\LifecycleController;
use App\Http\Controllers\Admin\LifecycleFollowUpController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\RegionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\RoleMenuPermissionController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\UserRoleController;
use Illuminate\Support\Facades\Route;

Route::prefix('admin')->group(function () {
    // 登录
    Route::post('/login', [AuthController::class, 'login'])->name('login');
    Route::middleware('auth:sanctum')->group(function () {
        // 退出
        Route::post('/logout', [AuthController::class, 'logout']);

        // 字典管理
        Route::prefix('dictionary')->group(function () {
            // 字典分类管理
            Route::get('/categories', [DictionaryController::class, 'categoryIndex']);
            Route::post('/categories', [DictionaryController::class, 'categoryStore']);
            Route::put('/categories/{category}', [DictionaryController::class, 'categoryUpdate']);
            Route::delete('/categories/{category}', [DictionaryController::class, 'categoryDestroy']);

            // 字典项管理
            Route::get('/items', [DictionaryController::class, 'itemIndex']);
            Route::post('/items', [DictionaryController::class, 'itemStore']);
            Route::put('/items/{item}', [DictionaryController::class, 'itemUpdate']);
            Route::delete('/items/{item}', [DictionaryController::class, 'itemDestroy']);

            // 根据分类编码获取字典项
            Route::get('/code/{categoryCode}', [DictionaryController::class, 'getByCode']);
            // 兼容旧API
            Route::get('/children/{categoryCode}', [DictionaryController::class, 'getChildren']);
        });

        // 分类管理
        Route::prefix('categories')->group(function () {
            Route::get('/', [CategoryController::class, 'index']);
            Route::post('/', [CategoryController::class, 'store']);
            Route::put('/{category}', [CategoryController::class, 'update']);
            Route::delete('/{category}', [CategoryController::class, 'destroy']);
            Route::get('/children/{parentId}', [CategoryController::class, 'getChildren']);
        });

        // 用户管理
        Route::apiResource('users', UserController::class);

        // 用户角色管理
        Route::prefix('users/{user}/roles')->group(function () {
            Route::post('/assign', [UserRoleController::class, 'assign']);
            Route::put('/sync', [UserRoleController::class, 'sync']);
            Route::delete('/remove', [UserRoleController::class, 'remove']);
        });

        // 角色管理
        Route::apiResource('roles', RoleController::class);

        // 角色菜单权限管理
        Route::prefix('roles/{role}/menu-permissions')->group(function () {
            Route::post('assign', [RoleController::class, 'assign']);
        });

        // 主体管理
        Route::apiResource('entities', EntityController::class);

        // 主体联系人管理
        Route::prefix('entities/{entity}/contacts')->group(function () {
            Route::get('/', [EntityContactController::class, 'index']);
            Route::post('/', [EntityContactController::class, 'store']);
            Route::put('/{contact}', [EntityContactController::class, 'update']);
            Route::delete('/{contact}', [EntityContactController::class, 'destroy']);
        });

        // 附件管理
        Route::prefix('attachments')->group(function () {
            Route::get('/', [AttachmentController::class, 'index']);
            Route::post('/upload', [AttachmentController::class, 'upload']);
            Route::get('/{attachment}', [AttachmentController::class, 'show']);
            Route::get('/{attachment}/download', [AttachmentController::class, 'download']);
            Route::delete('/{attachment}', [AttachmentController::class, 'destroy']);

            // OSS直传相关接口
            Route::post('/oss/signature', [AttachmentController::class, 'getPostSignature']);
            Route::post('/oss/callback', [AttachmentController::class, 'handleOssCallback']);

            // 业务关联接口
            Route::get('/by-business', [AttachmentController::class, 'getByBusiness']);
        });

        // 生命周期管理
        Route::prefix('lifecycles')->group(function () {
            Route::get('/', [LifecycleController::class, 'index']);
            Route::post('/', [LifecycleController::class, 'store']);
            Route::get('/{id}', [LifecycleController::class, 'show']);
            Route::put('/{id}', [LifecycleController::class, 'update']);
            Route::delete('/{id}', [LifecycleController::class, 'destroy']);

            // 获取验收人员列表
            Route::get('/entities/{entityId}/acceptance-personnel', [LifecycleController::class, 'getAcceptancePersonnel']);

            // 跟进记录管理
            Route::prefix('{lifecycleId}/follow-ups')->group(function () {
                Route::get('/', [LifecycleFollowUpController::class, 'index']);
                Route::post('/', [LifecycleFollowUpController::class, 'store']);
                Route::get('/{id}', [LifecycleFollowUpController::class, 'show']);
                Route::put('/{id}', [LifecycleFollowUpController::class, 'update']);
                Route::delete('/{id}', [LifecycleFollowUpController::class, 'destroy']);
            });
        });

        // 地区管理
        Route::prefix('regions')->group(function () {
            Route::get('/tree', [RegionController::class, 'tree']);
            Route::get('/children/{parentId}', [RegionController::class, 'children']);
            Route::get('/path/{code}', [RegionController::class, 'path']);
            Route::get('/search', [RegionController::class, 'search']);
            Route::get('/provinces', [RegionController::class, 'provinces']);
            Route::get('/cities/{provinceId}', [RegionController::class, 'cities']);
            Route::get('/districts/{cityId}', [RegionController::class, 'districts']);
        });

        // 资产管理
        Route::prefix('assets')->group(function () {
            Route::get('/', [AssetController::class, 'index']);
            Route::post('/', [AssetController::class, 'store']);
            Route::get('/main-assets', [AssetController::class, 'mainAssets']);
            Route::get('/{asset}', [AssetController::class, 'show']);
            Route::put('/{asset}', [AssetController::class, 'update']);
            Route::delete('/{asset}', [AssetController::class, 'destroy']);
        });

        // 菜单管理
        Route::prefix('menus')->group(function () {
            Route::get('/', [MenuController::class, 'index']);
            Route::get('/tree', [MenuController::class, 'tree']);
            Route::post('/', [MenuController::class, 'store']);
            Route::put('/{menu}', [MenuController::class, 'update']);
            Route::delete('/{menu}', [MenuController::class, 'destroy']);
        });
    });
});
