<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 只创建一个默认管理员
        User::create([
            'username' => 'admin',
            'tenant_id' => 1,
            'password' => Hash::make('123456'),
            'email' => null,
            'phone' => null,
            'status' => 'enable',
        ]);
    }
}
