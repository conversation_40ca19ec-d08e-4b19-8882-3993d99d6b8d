## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 主体管理
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/entities
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取主体列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      name:
        name: name
        description: 主体名称搜索
        required: false
        example: 测试公司
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tax_number:
        name: tax_number
        description: 税号搜索
        required: false
        example: 91110108MA01A2B3C4
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keywords:
        name: keywords
        description: 特征词搜索
        required: false
        example: 科技
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      keyword:
        name: keyword
        description: 通用搜索关键词（同时搜索名称、税号、特征词）
        required: false
        example: 测试
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      entity_type:
        name: entity_type
        description: 主体类型（字典code）
        required: false
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      name: 测试公司
      tax_number: 91110108MA01A2B3C4
      keywords: 科技
      keyword: 测试
      entity_type: manufacturer
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":null,"name":null,"tax_number":null,"entity_type":null,"address":null,"phone":null,"keywords":null,"remark":null,"created_by":null,"updated_by":null,"created_at":null,"updated_at":null},{"id":null,"name":null,"tax_number":null,"entity_type":null,"address":null,"phone":null,"keywords":null,"remark":null,"created_by":null,"updated_by":null,"created_at":null,"updated_at":null}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/entities
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建主体
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 主体名称
        required: true
        example: 测试科技有限公司
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tax_number:
        name: tax_number
        description: 税号
        required: false
        example: 91110108MA01A2B3C4
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      entity_type:
        name: entity_type
        description: 主体类型（字典code）
        required: true
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      address:
        name: address
        description: 地址
        required: false
        example: 北京市海淀区中关村大街1号
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: false
        example: 010-12345678
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      keywords:
        name: keywords
        description: 特征词（10字以内）
        required: false
        example: 科技创新
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      remark:
        name: remark
        description: 备注
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contacts:
        name: contacts
        description: 联系人列表
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      attachments:
        name: attachments
        description: 'The <code>id</code> of an existing record in the attachments table.'
        required: false
        example:
          - 16
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'contacts[].id':
        name: 'contacts[].id'
        description: 'The <code>id</code> of an existing record in the entity_contacts table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'contacts[].name':
        name: 'contacts[].name'
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].phone':
        name: 'contacts[].phone'
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].position':
        name: 'contacts[].position'
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'contacts[].department':
        name: 'contacts[].department'
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 测试科技有限公司
      tax_number: 91110108MA01A2B3C4
      entity_type: manufacturer
      address: 北京市海淀区中关村大街1号
      phone: 010-12345678
      keywords: 科技创新
      remark: architecto
      contacts:
        - architecto
      attachments:
        - 16
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/entities/{id}'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取主体详情
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/entities/{id}'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新主体
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 主体名称
        required: true
        example: 测试科技有限公司
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tax_number:
        name: tax_number
        description: 税号
        required: false
        example: 91110108MA01A2B3C4
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      entity_type:
        name: entity_type
        description: 主体类型（字典code）
        required: true
        example: manufacturer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      address:
        name: address
        description: 地址
        required: false
        example: 北京市海淀区中关村大街1号
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: false
        example: 010-12345678
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      keywords:
        name: keywords
        description: 特征词（10字以内）
        required: false
        example: 科技创新
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      remark:
        name: remark
        description: 备注
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contacts:
        name: contacts
        description: 联系人列表
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      attachments:
        name: attachments
        description: 'The <code>id</code> of an existing record in the attachments table.'
        required: false
        example:
          - 16
        type: 'integer[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'contacts[].id':
        name: 'contacts[].id'
        description: 联系人ID（更新时需要）
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      'contacts[].name':
        name: 'contacts[].name'
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].phone':
        name: 'contacts[].phone'
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      'contacts[].position':
        name: 'contacts[].position'
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      'contacts[].department':
        name: 'contacts[].department'
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 测试科技有限公司
      tax_number: 91110108MA01A2B3C4
      entity_type: manufacturer
      address: 北京市海淀区中关村大街1号
      phone: 010-12345678
      keywords: 科技创新
      remark: architecto
      contacts:
        - architecto
      attachments:
        - 16
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/entities/{id}'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除主体
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 16
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/entities/{entity_id}/contacts'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取主体的联系人列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 16
      entity: 1
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":null,"entity_id":null,"name":null,"phone":null,"position":null,"department":null,"created_by":null,"updated_by":null,"created_at":null,"updated_at":null},{"id":null,"entity_id":null,"name":null,"phone":null,"position":null,"department":null,"created_by":null,"updated_by":null,"created_at":null,"updated_at":null}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":10,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/entities/{entity_id}/contacts'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建联系人
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 16
      entity: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      position:
        name: position
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      department:
        name: department
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 张三
      phone: '13800138000'
      position: 总经理
      department: 管理部
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/entities/{entity_id}/contacts/{contact_id}'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新联系人
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_id:
        name: contact_id
        description: 'The ID of the contact.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact:
        name: contact
        description: 联系人ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 16
      contact_id: 16
      entity: 1
      contact: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 联系人姓名
        required: true
        example: 张三
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone:
        name: phone
        description: 联系电话
        required: true
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      position:
        name: position
        description: 职位
        required: false
        example: 总经理
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      department:
        name: department
        description: 部门
        required: false
        example: 管理部
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      name: 张三
      phone: '13800138000'
      position: 总经理
      department: 管理部
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/entities/{entity_id}/contacts/{contact_id}'
    metadata:
      groupName: 主体管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除联系人
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      entity_id:
        name: entity_id
        description: 'The ID of the entity.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_id:
        name: contact_id
        description: 'The ID of the contact.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity:
        name: entity
        description: 主体ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact:
        name: contact
        description: 联系人ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      entity_id: 16
      contact_id: 16
      entity: 1
      contact: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
