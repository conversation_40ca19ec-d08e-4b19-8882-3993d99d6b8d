<?php

namespace Tests\Feature;

use App\Models\Menu;
use App\Models\MenuPermission;
use App\Models\Role;
use App\Models\RoleMenuPermission;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RoleMenuPermissionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 运行种子文件
        $this->seed([
            \Database\Seeders\MenuSeeder::class,
            \Database\Seeders\RoleMenuPermissionSeeder::class,
        ]);
    }

    public function test_admin_role_has_all_permissions()
    {
        $adminRole = Role::where('name', 'admin')->first();
        $this->assertNotNull($adminRole);

        $totalMenus = Menu::count();
        $totalPermissions = MenuPermission::count();
        
        // 管理员应该有所有菜单的访问权限 + 所有操作权限
        $adminPermissions = RoleMenuPermission::where('role_id', $adminRole->id)->count();
        $this->assertEquals($totalMenus + $totalPermissions, $adminPermissions);
    }

    public function test_user_role_has_limited_permissions()
    {
        $userRole = Role::where('name', 'user')->first();
        $this->assertNotNull($userRole);

        // 普通用户应该只有基础菜单的访问权限
        $userPermissions = RoleMenuPermission::where('role_id', $userRole->id)->count();
        $this->assertGreaterThan(0, $userPermissions);
        
        // 普通用户权限应该少于管理员
        $adminPermissions = RoleMenuPermission::where('role_id', Role::where('name', 'admin')->first()->id)->count();
        $this->assertLessThan($adminPermissions, $userPermissions);
    }

    public function test_user_can_check_menu_access()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $user->roles()->attach($adminRole);

        $menu = Menu::first();
        $this->assertTrue($user->hasMenuAccess($menu->id));
    }

    public function test_user_can_check_menu_permission()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $user->roles()->attach($adminRole);

        $menuPermission = MenuPermission::first();
        $this->assertTrue($user->hasMenuPermission($menuPermission->menu_id, $menuPermission->id));
    }

    public function test_role_menu_permission_api_endpoints()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $user->roles()->attach($adminRole);

        // 测试获取角色权限
        $response = $this->actingAs($user, 'sanctum')
            ->getJson("/api/admin/roles/{$adminRole->id}/menu-permissions");
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'role',
                'permissions'
            ]);

        // 测试获取菜单列表用于权限分配
        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/admin/menus/for-assignment');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'menus'
            ]);
    }
}
