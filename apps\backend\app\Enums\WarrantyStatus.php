<?php

namespace App\Enums;

/**
 * 质保期状态
 * 
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 * 
 * @generated 2025-07-23 14:30:33
 */
enum WarrantyStatus: string
{
    case NORMAL = 'normal';
    case EXPIRED = 'expired';
    case RENEWED = 'renewed';
    case NONE = 'none';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::NORMAL => '正常',
            self::EXPIRED => '过期',
            self::RENEWED => '续期',
            self::NONE => '无',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }
}
