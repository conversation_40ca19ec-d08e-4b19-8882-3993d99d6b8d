name: 附件管理
description: 附件的上传、下载、删除等操作
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/attachments
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取附件列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页数量
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      file_name:
        name: file_name
        description: 文件名（模糊搜索）
        required: false
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_time:
        name: start_time
        description: 开始时间
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_time:
        name: end_time
        description: 结束时间
        required: false
        example: '2024-12-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 20
      file_name: example.pdf
      start_time: '2024-01-01'
      end_time: '2024-12-31'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":"","file_name":null,"file_path":null,"file_size":null,"mime_type":null,"storage_type":null,"md5_hash":null,"file_url":"","formatted_file_size":" bytes","created_at":null,"updated_at":null,"attachable_type":null,"attachable_id":null,"category":null,"description":null},{"id":"","file_name":null,"file_path":null,"file_size":null,"mime_type":null,"storage_type":null,"md5_hash":null,"file_url":"","formatted_file_size":" bytes","created_at":null,"updated_at":null,"attachable_type":null,"attachable_id":null,"category":null,"description":null}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/upload
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 上传附件（本地上传）
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      file:
        name: file
        description: 要上传的文件（最大10MB）
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters: []
    fileParameters:
      file: null
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attachments/{attachment}'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取附件详情
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachment:
        name: attachment
        description: 'The attachment.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachment: 16
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/attachments/{attachment}/download'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 下载附件
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachment:
        name: attachment
        description: 'The attachment.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachment: 16
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/attachments/{attachment}'
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 删除附件
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      attachment:
        name: attachment
        description: 'The attachment.'
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 附件ID
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      attachment: 16
      id: 16
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/oss/signature
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 获取OSS上传签名
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      filename:
        name: filename
        description: 文件名
        required: true
        example: example.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      filesize:
        name: filesize
        description: 文件大小（字节）
        required: true
        example: 1024000
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      mime_type:
        name: mime_type
        description: MIME类型
        required: true
        example: application/pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      md5_hash:
        name: md5_hash
        description: 文件MD5值（用于秒传）
        required: false
        example: 5d41402abc4b2a76b9719d911017c592
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      filename: example.pdf
      filesize: 1024000
      mime_type: application/pdf
      md5_hash: 5d41402abc4b2a76b9719d911017c592
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/attachments/oss/callback
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: OSS上传回调
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      upload_id:
        name: upload_id
        description: 上传ID
        required: true
        example: 550e8400-e29b-41d4-a716-446655440000
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      object_key:
        name: object_key
        description: OSS对象键值
        required: true
        example: attachments/2024/01/01/xxx.pdf
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      upload_id: 550e8400-e29b-41d4-a716-446655440000
      object_key: attachments/2024/01/01/xxx.pdf
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/attachments/by-business
    metadata:
      groupName: 附件管理
      groupDescription: 附件的上传、下载、删除等操作
      subgroup: ''
      subgroupDescription: ''
      title: 根据业务ID获取附件列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      attachable_type:
        name: attachable_type
        description: 业务类型
        required: true
        example: App\Models\Entity
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 业务ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category:
        name: category
        description: 附件分类
        required: false
        example: contract
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      attachable_type: App\Models\Entity
      attachable_id: 1
      category: contract
    bodyParameters:
      attachable_type:
        name: attachable_type
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: ''
        required: true
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      attachable_type: architecto
      attachable_id: 16
      category: architecto
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
