{"variable": [{"id": "baseUrl", "key": "baseUrl", "type": "string", "name": "string", "value": "http://localhost"}], "info": {"name": "Laravel API Documentation", "_postman_id": "85dcc171-deaf-4d8b-a920-d12c9d1bcd0b", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "主体管理", "description": "", "item": [{"name": "获取主体列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities", "query": [{"key": "name", "value": "%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8", "description": "主体名称搜索", "disabled": false}, {"key": "tax_number", "value": "91110108MA01A2B3C4", "description": "税号搜索", "disabled": false}, {"key": "keywords", "value": "%E7%A7%91%E6%8A%80", "description": "特征词搜索", "disabled": false}, {"key": "keyword", "value": "%E6%B5%8B%E8%AF%95", "description": "通用搜索关键词（同时搜索名称、税号、特征词）", "disabled": false}, {"key": "entity_type", "value": "manufacturer", "description": "主体类型（字典code）", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/entities?name=%E6%B5%8B%E8%AF%95%E5%85%AC%E5%8F%B8&tax_number=91110108MA01A2B3C4&keywords=%E7%A7%91%E6%8A%80&keyword=%E6%B5%8B%E8%AF%95&entity_type=manufacturer&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":null,\"name\":null,\"tax_number\":null,\"entity_type\":null,\"address\":null,\"phone\":null,\"keywords\":null,\"remark\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":null,\"updated_at\":null},{\"id\":null,\"name\":null,\"tax_number\":null,\"entity_type\":null,\"address\":null,\"phone\":null,\"keywords\":null,\"remark\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":null,\"updated_at\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建主体", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities", "query": [], "raw": "{{baseUrl}}/api/admin/entities"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"测试科技有限公司\",\"tax_number\":\"91110108MA01A2B3C4\",\"entity_type\":\"manufacturer\",\"address\":\"北京市海淀区中关村大街1号\",\"phone\":\"010-12345678\",\"keywords\":\"科技创新\",\"remark\":\"architecto\",\"contacts\":[\"architecto\"],\"attachments\":[16]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取主体详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:id", "variable": [{"id": "id", "key": "id", "value": "16", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新主体", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:id", "variable": [{"id": "id", "key": "id", "value": "16", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"测试科技有限公司\",\"tax_number\":\"91110108MA01A2B3C4\",\"entity_type\":\"manufacturer\",\"address\":\"北京市海淀区中关村大街1号\",\"phone\":\"010-12345678\",\"keywords\":\"科技创新\",\"remark\":\"architecto\",\"contacts\":[\"architecto\"],\"attachments\":[16]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除主体", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:id", "variable": [{"id": "id", "key": "id", "value": "16", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取主体的联系人列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "10", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts?page=1&per_page=10", "variable": [{"id": "entity_id", "key": "entity_id", "value": "16", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":null,\"entity_id\":null,\"name\":null,\"phone\":null,\"position\":null,\"department\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":null,\"updated_at\":null},{\"id\":null,\"entity_id\":null,\"name\":null,\"phone\":null,\"position\":null,\"department\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":null,\"updated_at\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":10,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建联系人", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts", "variable": [{"id": "entity_id", "key": "entity_id", "value": "16", "description": "The ID of the entity."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"张三\",\"phone\":\"13800138000\",\"position\":\"总经理\",\"department\":\"管理部\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新联系人", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts/:contact_id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts/:contact_id", "variable": [{"id": "entity_id", "key": "entity_id", "value": "16", "description": "The ID of the entity."}, {"id": "contact_id", "key": "contact_id", "value": "16", "description": "The ID of the contact."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}, {"id": "contact", "key": "contact", "value": "1", "description": "联系人ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"张三\",\"phone\":\"13800138000\",\"position\":\"总经理\",\"department\":\"管理部\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除联系人", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/entities/:entity_id/contacts/:contact_id", "query": [], "raw": "{{baseUrl}}/api/admin/entities/:entity_id/contacts/:contact_id", "variable": [{"id": "entity_id", "key": "entity_id", "value": "16", "description": "The ID of the entity."}, {"id": "contact_id", "key": "contact_id", "value": "16", "description": "The ID of the contact."}, {"id": "entity", "key": "entity", "value": "1", "description": "主体ID"}, {"id": "contact", "key": "contact", "value": "1", "description": "联系人ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "分类管理", "description": "\n系统分类管理接口", "item": [{"name": "获取分类树", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories", "query": [{"key": "status", "value": "1", "description": "状态筛选（1启用 0禁用）", "disabled": false}], "raw": "{{baseUrl}}/api/admin/categories?status=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有分类的树形结构数据"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories", "query": [], "raw": "{{baseUrl}}/api/admin/categories"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"电子设备\",\"code\":\"electronic\",\"parent_id\":0,\"sort\":0,\"status\":1,\"remark\":\"电子设备分类\"}"}, "description": "创建新的分类"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/:category_id", "query": [], "raw": "{{baseUrl}}/api/admin/categories/:category_id", "variable": [{"id": "category_id", "key": "category_id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "分类ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"电子设备\",\"code\":\"electronic\",\"parent_id\":0,\"sort\":0,\"status\":1,\"remark\":\"电子设备分类\"}"}, "description": "更新指定的分类信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/:category_id", "query": [], "raw": "{{baseUrl}}/api/admin/categories/:category_id", "variable": [{"id": "category_id", "key": "category_id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "分类ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "删除指定的分类（分类下存在子分类时无法删除）"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取子分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/categories/children/:parentId", "query": [], "raw": "{{baseUrl}}/api/admin/categories/children/:parentId", "variable": [{"id": "parentId", "key": "parentId", "value": "0", "description": "父级分类ID，传0获取顶级分类"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取指定分类的直接子分类列表"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "地区管理", "description": "\n省市区地区数据管理接口", "item": [{"name": "获取地区树形结构", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/tree", "query": [{"key": "deep", "value": "3", "description": "获取的层级深度（可选，默认3级）", "disabled": false}], "raw": "{{baseUrl}}/api/admin/regions/tree?deep=3"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取完整的省市区三级树形结构数据，用于级联选择器"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取指定父级的子地区", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/children/:parentId", "query": [], "raw": "{{baseUrl}}/api/admin/regions/children/:parentId", "variable": [{"id": "parentId", "key": "parentId", "value": "11", "description": "父级地区ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "用于懒加载获取子地区数据"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "根据地区代码获取完整路径", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/path/:code", "query": [], "raw": "{{baseUrl}}/api/admin/regions/path/:code", "variable": [{"id": "code", "key": "code", "value": "110101000000", "description": "地区代码"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "根据区县代码获取省市区完整路径信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "搜索地区", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/search", "query": [{"key": "keyword", "value": "%E5%8C%97%E4%BA%AC", "description": "搜索关键词", "disabled": false}, {"key": "limit", "value": "20", "description": "返回结果数量限制（默认20，最大50）", "disabled": false}, {"key": "deep", "value": "2", "description": "搜索的层级（0省1市2区县，默认搜索所有层级）", "disabled": false}], "raw": "{{baseUrl}}/api/admin/regions/search?keyword=%E5%8C%97%E4%BA%AC&limit=20&deep=2"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"keyword\":\"bngzmiyvdljnikhw\",\"limit\":22,\"deep\":\"1\"}"}, "description": "根据关键词搜索地区，支持名称和拼音搜索"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取省份列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/provinces", "query": [], "raw": "{{baseUrl}}/api/admin/regions/provinces"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有省级行政区列表"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取指定省份的城市列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/cities/:provinceId", "query": [], "raw": "{{baseUrl}}/api/admin/regions/cities/:provinceId", "variable": [{"id": "provinceId", "key": "provinceId", "value": "11", "description": "省份ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取指定城市的区县列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/regions/districts/:cityId", "query": [], "raw": "{{baseUrl}}/api/admin/regions/districts/:cityId", "variable": [{"id": "cityId", "key": "cityId", "value": "1101", "description": "城市ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "字典管理", "description": "\n系统字典配置管理接口", "item": [{"name": "获取字典分类列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories", "query": [{"key": "name", "value": "%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B", "description": "分类名称", "disabled": false}, {"key": "code", "value": "device_type", "description": "分类编码", "disabled": false}, {"key": "is_enabled", "value": "1", "description": "是否启用", "disabled": false}], "raw": "{{baseUrl}}/api/admin/dictionary/categories?name=%E8%AE%BE%E5%A4%87%E7%B1%BB%E5%9E%8B&code=device_type&is_enabled=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有字典分类，支持条件筛选"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建字典分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/categories"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"code\":\"device_type\",\"name\":\"设备类型\",\"description\":\"设备类型分类\",\"sort\":1,\"is_enabled\":false}"}, "description": "创建新的字典分类"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新字典分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories/:category_id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/categories/:category_id", "variable": [{"id": "category_id", "key": "category_id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "字典分类ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"code\":\"device_type\",\"name\":\"设备类型\",\"description\":\"设备类型分类\",\"sort\":1,\"is_enabled\":false}"}, "description": "更新指定的字典分类信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除字典分类", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/categories/:category_id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/categories/:category_id", "variable": [{"id": "category_id", "key": "category_id", "value": "1", "description": "The ID of the category."}, {"id": "category", "key": "category", "value": "1", "description": "字典分类ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "删除指定的字典分类（分类下存在字典项时无法删除）"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取字典项列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items", "query": [{"key": "category_id", "value": "1", "description": "分类ID", "disabled": false}, {"key": "code", "value": "desktop", "description": "字典编码", "disabled": false}, {"key": "value", "value": "%E5%8F%B0%E5%BC%8F%E6%9C%BA", "description": "字典值", "disabled": false}, {"key": "is_enabled", "value": "1", "description": "是否启用", "disabled": false}], "raw": "{{baseUrl}}/api/admin/dictionary/items?category_id=1&code=desktop&value=%E5%8F%B0%E5%BC%8F%E6%9C%BA&is_enabled=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取所有字典项，支持条件筛选"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/items"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"category_id\":1,\"code\":\"desktop\",\"value\":\"台式机\",\"label\":\"台式计算机\",\"sort\":1,\"color\":\"#FF5733\",\"icon\":\"el-icon-monitor\",\"config\":{\"key\":\"value\"},\"remark\":\"用于标识台式计算机类型\",\"is_enabled\":false}"}, "description": "在指定分类下创建新的字典项"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items/:item_id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/items/:item_id", "variable": [{"id": "item_id", "key": "item_id", "value": "1", "description": "The ID of the item."}, {"id": "item", "key": "item", "value": "1", "description": "字典项ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"category_id\":1,\"code\":\"desktop\",\"value\":\"台式机\",\"label\":\"台式计算机\",\"sort\":1,\"color\":\"#FF5733\",\"icon\":\"el-icon-monitor\",\"config\":{\"key\":\"value\"},\"remark\":\"用于标识台式计算机类型\",\"is_enabled\":false}"}, "description": "更新指定的字典项信息"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/items/:item_id", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/items/:item_id", "variable": [{"id": "item_id", "key": "item_id", "value": "1", "description": "The ID of the item."}, {"id": "item", "key": "item", "value": "1", "description": "字典项ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "删除指定的字典项"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "根据分类编码获取字典项", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/dictionary/code/:categoryCode", "query": [], "raw": "{{baseUrl}}/api/admin/dictionary/code/:categoryCode", "variable": [{"id": "categoryCode", "key": "categoryCode", "value": "device_type", "description": "分类编码"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取指定分类编码下的所有启用的字典项"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "授权", "description": "\n管理员认证相关接口", "item": [{"name": "登录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/login", "query": [], "raw": "{{baseUrl}}/api/admin/login"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"username\":\"admin\",\"password\":\"password123\"}"}, "description": "管理员登录接口，验证用户名和密码后返回访问令牌", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 422, "body": "{\"message\":\"账号或密码错误\",\"errors\":{\"username\":[\"账号或密码错误\"]}}", "name": ""}]}, {"name": "退出登录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/logout", "query": [], "raw": "{{baseUrl}}/api/admin/logout"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "撤销当前访问令牌，退出登录状态"}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "生命周期管理", "description": "\n管理设备生命周期记录", "item": [{"name": "获取生命周期列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页数量.", "disabled": false}, {"key": "asset_id", "value": "AST-2024-001", "description": "资产ID筛选.", "disabled": false}, {"key": "type", "value": "installation", "description": "类型筛选.", "disabled": false}, {"key": "start_date", "value": "2024-01-01", "description": "开始日期筛选.", "disabled": false}, {"key": "end_date", "value": "2024-12-31", "description": "结束日期筛选.", "disabled": false}, {"key": "initiator_id", "value": "1", "description": "发起人ID筛选.", "disabled": false}, {"key": "acceptance_entity_id", "value": "1", "description": "验收主体ID筛选.", "disabled": false}], "raw": "{{baseUrl}}/api/admin/lifecycles?page=1&per_page=20&asset_id=AST-2024-001&type=installation&start_date=2024-01-01&end_date=2024-12-31&initiator_id=1&acceptance_entity_id=1"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":null,\"asset_id\":null,\"type\":null,\"date\":null,\"initiator\":null,\"content\":null,\"acceptance_entity\":null,\"acceptance_personnel\":null,\"acceptance_time\":null,\"created_at\":null,\"updated_at\":null},{\"id\":null,\"asset_id\":null,\"type\":null,\"date\":null,\"initiator\":null,\"content\":null,\"acceptance_entity\":null,\"acceptance_personnel\":null,\"acceptance_time\":null,\"created_at\":null,\"updated_at\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建生命周期", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"asset_id\":1,\"type\":\"installation\",\"date\":\"2024-01-15\",\"initiator_id\":1,\"content\":\"采购了一批新的服务器设备\",\"assistants\":[2,3],\"acceptance_entity_id\":1,\"acceptance_personnel_id\":1,\"acceptance_time\":\"2024-01-20 14:30:00\",\"attachments\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取生命周期详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新生命周期", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"asset_id\":1,\"type\":\"installation\",\"date\":\"2024-01-15\",\"initiator_id\":1,\"content\":\"采购了一批新的服务器设备\",\"assistants\":[2,3],\"acceptance_entity_id\":1,\"acceptance_personnel_id\":1,\"acceptance_time\":\"2024-01-20 14:30:00\",\"attachments\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除生命周期", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "生命周期ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取验收人员列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/entities/:entityId/acceptance-personnel", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/entities/:entityId/acceptance-personnel", "variable": [{"id": "entityId", "key": "entityId", "value": "1", "description": "验收主体ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取生命周期的跟进记录列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "创建跟进记录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"date\":\"2024-01-16\",\"person_id\":2,\"content\":\"已联系供应商确认发货时间\",\"attachments\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取跟进记录详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups/:id", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}, {"id": "id", "key": "id", "value": "1", "description": "跟进记录ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新跟进记录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups/:id", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}, {"id": "id", "key": "id", "value": "1", "description": "跟进记录ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"date\":\"2024-01-16\",\"person_id\":2,\"content\":\"已联系供应商确认发货时间\",\"attachments\":[1,2,3]}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除跟进记录", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/lifecycles/:lifecycleId/follow-ups/:id", "query": [], "raw": "{{baseUrl}}/api/admin/lifecycles/:lifecycleId/follow-ups/:id", "variable": [{"id": "lifecycleId", "key": "lifecycleId", "value": "1", "description": "生命周期ID."}, {"id": "id", "key": "id", "value": "1", "description": "跟进记录ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "用户管理", "description": "", "item": [{"name": "获取用户列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users", "query": [{"key": "keyword", "value": "admin", "description": "搜索关键词（用户名、手机号、邮箱）", "disabled": false}, {"key": "status", "value": "enable", "description": "用户状态（enable/disable）", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/users?keyword=admin&status=enable&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":1,\"tenant_id\":1,\"username\":\"admin\",\"email\":null,\"phone\":null,\"avatar\":null,\"avatar_id\":null,\"status\":\"enable\",\"status_label\":\"\\u542f\\u7528\",\"roles\":[{\"id\":1,\"name\":\"admin\",\"description\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\"}],\"created_at\":\"2025-07-24 09:20:45\",\"updated_at\":\"2025-07-24 09:20:45\"},{\"id\":1,\"tenant_id\":1,\"username\":\"admin\",\"email\":null,\"phone\":null,\"avatar\":null,\"avatar_id\":null,\"status\":\"enable\",\"status_label\":\"\\u542f\\u7528\",\"roles\":[{\"id\":1,\"name\":\"admin\",\"description\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\"}],\"created_at\":\"2025-07-24 09:20:45\",\"updated_at\":\"2025-07-24 09:20:45\"}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建用户", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users", "query": [], "raw": "{{baseUrl}}/api/admin/users"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"phone\":\"13800138000\",\"status\":\"enable\",\"avatar_id\":16,\"password\":\"password123\"}"}, "description": ""}, "response": [{"header": [], "code": 201, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\"\n  }\n}", "name": ""}]}, {"name": "获取用户详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:id", "query": [], "raw": "{{baseUrl}}/api/admin/users/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"username\": \"admin\",\n    \"email\": \"<EMAIL>\"\n  }\n}", "name": ""}]}, {"name": "更新用户", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:id", "query": [], "raw": "{{baseUrl}}/api/admin/users/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"phone\":\"13800138000\",\"status\":\"enable\",\"avatar_id\":16,\"password\":\"newpassword\"}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\"\n  }\n}", "name": ""}]}, {"name": "删除用户", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:id", "query": [], "raw": "{{baseUrl}}/api/admin/users/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 204, "body": "{}", "name": ""}]}, {"name": "为用户分配角色（追加式）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:user_id/roles/assign", "query": [], "raw": "{{baseUrl}}/api/admin/users/:user_id/roles/assign", "variable": [{"id": "user_id", "key": "user_id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"role_ids\":[1,2]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"id\": 1,\n  \"name\": \"张三\",\n  \"email\": \"<EMAIL>\",\n  \"roles\": [\n    {\n      \"id\": 1,\n      \"name\": \"管理员\",\n      \"description\": \"系统管理员角色\"\n    },\n    {\n      \"id\": 2,\n      \"name\": \"普通用户\",\n      \"description\": \"普通用户角色\"\n    }\n  ]\n}", "name": ""}]}, {"name": "同步用户角色（覆盖式）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:user_id/roles/sync", "query": [], "raw": "{{baseUrl}}/api/admin/users/:user_id/roles/sync", "variable": [{"id": "user_id", "key": "user_id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"role_ids\":[1,2]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"id\": 1,\n  \"name\": \"张三\",\n  \"email\": \"<EMAIL>\",\n  \"roles\": [\n    {\n      \"id\": 1,\n      \"name\": \"管理员\",\n      \"description\": \"系统管理员角色\"\n    },\n    {\n      \"id\": 2,\n      \"name\": \"普通用户\",\n      \"description\": \"普通用户角色\"\n    }\n  ]\n}", "name": ""}]}, {"name": "移除用户角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/users/:user_id/roles/remove", "query": [], "raw": "{{baseUrl}}/api/admin/users/:user_id/roles/remove", "variable": [{"id": "user_id", "key": "user_id", "value": "1", "description": "The ID of the user."}, {"id": "user", "key": "user", "value": "1", "description": "用户ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"role_ids\":[1,2]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"id\": 1,\n  \"name\": \"张三\",\n  \"email\": \"<EMAIL>\",\n  \"roles\": [\n    {\n      \"id\": 1,\n      \"name\": \"管理员\",\n      \"description\": \"系统管理员角色\"\n    }\n  ]\n}", "name": ""}]}]}, {"name": "菜单管理", "description": "\n系统菜单管理接口", "item": [{"name": "获取菜单列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus", "query": [], "raw": "{{baseUrl}}/api/admin/menus"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构"}, "response": [{"header": [], "code": 200, "body": "{\n  \"menuList\": [\n    {\n      \"id\": 1,\n      \"parent_id\": 0,\n      \"name\": \"User\",\n      \"path\": \"/user\",\n      \"component\": \"User\",\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"label\": \"user\",\n      \"sort\": 1,\n      \"is_hide\": false,\n      \"is_hide_tab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"is_iframe\": false,\n      \"keep_alive\": true,\n      \"is_first_level\": false,\n      \"fixed_tab\": false,\n      \"active_path\": \"/user\",\n      \"is_full_page\": false,\n      \"show_badge\": false,\n      \"show_text_badge\": \"new\",\n      \"status\": true,\n      \"meta\": {\n        \"title\": \"用户管理\",\n        \"icon\": \"user\",\n        \"keepAlive\": true,\n        \"showBadge\": false,\n        \"showTextBadge\": \"new\",\n        \"isHide\": false,\n        \"isHideTab\": false,\n        \"link\": \"https://www.baidu.com\",\n        \"isIframe\": false,\n        \"authList\": [\n          {\n            \"title\": \"用户列表\",\n            \"authMark\": \"user:list\"\n          }\n        ],\n        \"isFirstLevel\": false,\n        \"fixedTab\": false,\n        \"activePath\": \"/user\",\n        \"isFullPage\": false\n      }\n    }\n  ]\n}", "name": ""}]}, {"name": "获取菜单树", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus/tree", "query": [], "raw": "{{baseUrl}}/api/admin/menus/tree"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "用于选择父级菜单"}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": [\n    {\n      \"id\": 1,\n      \"parent_id\": 0,\n      \"name\": \"User\",\n      \"path\": \"/user\",\n      \"component\": \"User\",\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"label\": \"user\",\n      \"sort\": 1,\n      \"is_hide\": false,\n      \"is_hide_tab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"is_iframe\": false,\n      \"keep_alive\": true,\n      \"is_first_level\": false,\n      \"fixed_tab\": false,\n      \"active_path\": \"/user\",\n      \"is_full_page\": false,\n      \"show_badge\": false,\n      \"show_text_badge\": \"new\",\n      \"status\": true,\n      \"meta\": {\n        \"title\": \"用户管理\",\n        \"icon\": \"user\",\n        \"keepAlive\": true,\n        \"showBadge\": false,\n        \"showTextBadge\": \"new\",\n        \"isHide\": false,\n        \"isHideTab\": false,\n        \"link\": \"https://www.baidu.com\",\n        \"isIframe\": false,\n        \"authList\": [\n          {\n            \"title\": \"用户列表\",\n            \"authMark\": \"user:list\"\n          }\n        ],\n        \"isFirstLevel\": false,\n        \"fixedTab\": false,\n        \"activePath\": \"/user\",\n        \"isFullPage\": false\n      },\n      \"children\": [\n        {\n          \"id\": 2,\n          \"parent_id\": 1,\n          \"name\": \"UserList\",\n          \"path\": \"/user/list\",\n          \"component\": \"UserList\",\n          \"title\": \"用户列表\",\n          \"icon\": \"user\",\n          \"label\": \"user:list\",\n          \"sort\": 1,\n          \"is_hide\": false,\n          \"is_hide_tab\": false,\n          \"link\": \"https://www.baidu.com\",\n          \"is_iframe\": false,\n          \"keep_alive\": true,\n          \"is_first_level\": false,\n          \"fixed_tab\": false,\n          \"active_path\": \"/user/list\",\n          \"is_full_page\": false,\n          \"show_badge\": false,\n          \"show_text_badge\": \"new\",\n          \"status\": true,\n          \"meta\": {\n            \"title\": \"用户列表\",\n            \"icon\": \"user\",\n            \"keepAlive\": true,\n            \"showBadge\": false,\n            \"showTextBadge\": \"new\",\n            \"isHide\": false,\n            \"isHideTab\": false,\n            \"link\": \"https://www.baidu.com\",\n            \"isIframe\": false,\n            \"authList\": [\n              {\n                \"title\": \"用户列表\",\n                \"authMark\": \"user:list\"\n              }\n            ],\n            \"isFirstLevel\": false,\n            \"fixedTab\": false,\n            \"activePath\": \"/user/list\",\n            \"isFullPage\": false\n          }\n        }\n      ]\n    }\n  ]\n}", "name": ""}]}, {"name": "创建菜单", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus", "query": [], "raw": "{{baseUrl}}/api/admin/menus"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"parent_id\":1,\"name\":\"用户管理\",\"path\":\"\\/user\",\"component\":\"User\",\"title\":\"用户管理\",\"icon\":\"user\",\"label\":\"user\",\"sort\":1,\"is_hide\":false,\"is_hide_tab\":false,\"link\":\"https:\\/\\/www.baidu.com\",\"is_iframe\":false,\"keep_alive\":true,\"is_first_level\":false,\"fixed_tab\":false,\"active_path\":\"\\/user\",\"is_full_page\":false,\"show_badge\":false,\"show_text_badge\":\"new\",\"status\":true,\"permissions\":[{\"title\":\"用户列表\",\"auth_mark\":\"user:list\",\"sort\":1}]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"parent_id\": 0,\n    \"name\": \"User\",\n    \"path\": \"/user\",\n    \"component\": \"User\",\n    \"title\": \"用户管理\",\n    \"icon\": \"user\",\n    \"label\": \"user\",\n    \"sort\": 1,\n    \"is_hide\": false,\n    \"is_hide_tab\": false,\n    \"link\": \"https://www.baidu.com\",\n    \"is_iframe\": false,\n    \"keep_alive\": true,\n    \"is_first_level\": false,\n    \"fixed_tab\": false,\n    \"active_path\": \"/user\",\n    \"is_full_page\": false,\n    \"show_badge\": false,\n    \"show_text_badge\": \"new\",\n    \"status\": true,\n    \"meta\": {\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"keepAlive\": true,\n      \"showBadge\": false,\n      \"showTextBadge\": \"new\",\n      \"isHide\": false,\n      \"isHideTab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"isIframe\": false,\n      \"authList\": [\n        {\n          \"title\": \"用户列表\",\n          \"authMark\": \"user:list\"\n        }\n      ],\n      \"isFirstLevel\": false,\n      \"fixedTab\": false,\n      \"activePath\": \"/user\",\n      \"isFullPage\": false\n    }\n  }\n}", "name": ""}]}, {"name": "更新菜单", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus/:menu_id", "query": [], "raw": "{{baseUrl}}/api/admin/menus/:menu_id", "variable": [{"id": "menu_id", "key": "menu_id", "value": "1", "description": "The ID of the menu."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"parent_id\":1,\"name\":\"用户管理\",\"path\":\"\\/user\",\"component\":\"User\",\"title\":\"用户管理\",\"icon\":\"user\",\"label\":\"user\",\"sort\":1,\"is_hide\":false,\"is_hide_tab\":false,\"link\":\"https:\\/\\/www.baidu.com\",\"is_iframe\":false,\"keep_alive\":true,\"is_first_level\":false,\"fixed_tab\":false,\"active_path\":\"\\/user\",\"is_full_page\":false,\"show_badge\":false,\"show_text_badge\":\"new\",\"status\":true,\"permissions\":[{\"title\":\"用户列表\",\"auth_mark\":\"user:list\",\"sort\":1}]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"data\": {\n    \"id\": 1,\n    \"parent_id\": 0,\n    \"name\": \"User\",\n    \"path\": \"/user\",\n    \"component\": \"User\",\n    \"title\": \"用户管理\",\n    \"icon\": \"user\",\n    \"label\": \"user\",\n    \"sort\": 1,\n    \"is_hide\": false,\n    \"is_hide_tab\": false,\n    \"link\": \"https://www.baidu.com\",\n    \"is_iframe\": false,\n    \"keep_alive\": true,\n    \"is_first_level\": false,\n    \"fixed_tab\": false,\n    \"active_path\": \"/user\",\n    \"is_full_page\": false,\n    \"show_badge\": false,\n    \"show_text_badge\": \"new\",\n    \"status\": true,\n    \"meta\": {\n      \"title\": \"用户管理\",\n      \"icon\": \"user\",\n      \"keepAlive\": true,\n      \"showBadge\": false,\n      \"showTextBadge\": \"new\",\n      \"isHide\": false,\n      \"isHideTab\": false,\n      \"link\": \"https://www.baidu.com\",\n      \"isIframe\": false,\n      \"authList\": [\n        {\n          \"title\": \"用户列表\",\n          \"authMark\": \"user:list\"\n        }\n      ],\n      \"isFirstLevel\": false,\n      \"fixedTab\": false,\n      \"activePath\": \"/user\",\n      \"isFullPage\": false\n    }\n  }\n}", "name": ""}]}, {"name": "删除菜单", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/menus/:menu_id", "query": [], "raw": "{{baseUrl}}/api/admin/menus/:menu_id", "variable": [{"id": "menu_id", "key": "menu_id", "value": "1", "description": "The ID of the menu."}, {"id": "menu", "key": "menu", "value": "1", "description": "菜单ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"message\": \"菜单删除成功\"\n}", "name": ""}]}]}, {"name": "角色管理", "description": "\n管理系统角色", "item": [{"name": "获取角色列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles", "query": [{"key": "search", "value": "admin", "description": "搜索关键词（角色名称）.", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数.", "disabled": false}], "raw": "{{baseUrl}}/api/admin/roles?search=admin&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":1,\"name\":\"admin\",\"guard_name\":null,\"description\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\",\"menus\":[{\"menu_id\":1,\"permission_ids\":[]},{\"menu_id\":2,\"permission_ids\":[]},{\"menu_id\":3,\"permission_ids\":[]},{\"menu_id\":4,\"permission_ids\":[1,2,3,4,5]},{\"menu_id\":5,\"permission_ids\":[6,7,8,9]},{\"menu_id\":6,\"permission_ids\":[]},{\"menu_id\":7,\"permission_ids\":[10,11,12]},{\"menu_id\":8,\"permission_ids\":[]},{\"menu_id\":9,\"permission_ids\":[13,14,15]},{\"menu_id\":10,\"permission_ids\":[16,17,18]},{\"menu_id\":11,\"permission_ids\":[19,20,21]},{\"menu_id\":12,\"permission_ids\":[22,23,24]},{\"menu_id\":13,\"permission_ids\":[25,26,27,28]},{\"menu_id\":14,\"permission_ids\":[29,30]}],\"created_at\":\"2025-07-24 09:20:45\",\"updated_at\":\"2025-07-24 09:20:45\"},{\"id\":1,\"name\":\"admin\",\"guard_name\":null,\"description\":\"\\u7cfb\\u7edf\\u7ba1\\u7406\\u5458\",\"menus\":[{\"menu_id\":1,\"permission_ids\":[]},{\"menu_id\":2,\"permission_ids\":[]},{\"menu_id\":3,\"permission_ids\":[]},{\"menu_id\":4,\"permission_ids\":[1,2,3,4,5]},{\"menu_id\":5,\"permission_ids\":[6,7,8,9]},{\"menu_id\":6,\"permission_ids\":[]},{\"menu_id\":7,\"permission_ids\":[10,11,12]},{\"menu_id\":8,\"permission_ids\":[]},{\"menu_id\":9,\"permission_ids\":[13,14,15]},{\"menu_id\":10,\"permission_ids\":[16,17,18]},{\"menu_id\":11,\"permission_ids\":[19,20,21]},{\"menu_id\":12,\"permission_ids\":[22,23,24]},{\"menu_id\":13,\"permission_ids\":[25,26,27,28]},{\"menu_id\":14,\"permission_ids\":[29,30]}],\"created_at\":\"2025-07-24 09:20:45\",\"updated_at\":\"2025-07-24 09:20:45\"}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles", "query": [], "raw": "{{baseUrl}}/api/admin/roles"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"管理员\",\"description\":\"系统管理员角色\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取角色详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"管理员\",\"description\":\"系统管理员角色\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除角色", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:id", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:id", "variable": [{"id": "id", "key": "id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "分配菜单权限", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/roles/:role_id/menu-permissions/assign", "query": [], "raw": "{{baseUrl}}/api/admin/roles/:role_id/menu-permissions/assign", "variable": [{"id": "role_id", "key": "role_id", "value": "1", "description": "The ID of the role."}, {"id": "role", "key": "role", "value": "1", "description": "角色ID."}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"permissions\":[{\"menu_id\":1,\"permission_ids\":[1,2,3,4,5]},{\"menu_id\":1,\"permission_ids\":[1,2,3,4,5]}]}"}, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\n  \"message\": \"权限分配成功\"\n}", "name": ""}]}]}, {"name": "资产管理", "description": "", "item": [{"name": "获取资产列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets", "query": [{"key": "name", "value": "%E5%8A%9E%E5%85%AC%E7%94%B5%E8%84%91", "description": "资产名称搜索", "disabled": false}, {"key": "brand", "value": "%E8%81%94%E6%83%B3", "description": "品牌搜索", "disabled": false}, {"key": "serial_number", "value": "ABC123456", "description": "序列号搜索", "disabled": false}, {"key": "keyword", "value": "%E8%81%94%E6%83%B3", "description": "通用搜索关键词（同时搜索名称、品牌、型号、序列号）", "disabled": false}, {"key": "asset_category_id", "value": "1", "description": "资产分类ID", "disabled": false}, {"key": "department_category_id", "value": "2", "description": "科室分类ID", "disabled": false}, {"key": "industry_category_id", "value": "3", "description": "行业分类ID", "disabled": false}, {"key": "asset_status", "value": "in_use", "description": "资产状态（字典code）", "disabled": false}, {"key": "asset_condition", "value": "brand_new", "description": "成色（字典code）", "disabled": false}, {"key": "asset_source", "value": "purchase", "description": "资产来源（字典code）", "disabled": false}, {"key": "is_accessory", "value": "", "description": "是否附属设备", "disabled": true}, {"key": "parent_id", "value": "1", "description": "主设备ID", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/assets?name=%E5%8A%9E%E5%85%AC%E7%94%B5%E8%84%91&brand=%E8%81%94%E6%83%B3&serial_number=ABC123456&keyword=%E8%81%94%E6%83%B3&asset_category_id=1&department_category_id=2&industry_category_id=3&asset_status=in_use&asset_condition=brand_new&asset_source=purchase&is_accessory=&parent_id=1&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":null,\"name\":null,\"brand\":null,\"model\":null,\"serial_number\":null,\"asset_category_id\":null,\"department_category_id\":null,\"industry_category_id\":null,\"asset_source\":null,\"asset_status\":null,\"asset_condition\":null,\"parent_id\":null,\"children_count\":0,\"region_code\":null,\"detailed_address\":null,\"start_date\":null,\"warranty_period\":null,\"warranty_alert\":null,\"maintenance_cycle\":null,\"expected_years\":null,\"related_entities\":null,\"remark\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":null,\"updated_at\":null},{\"id\":null,\"name\":null,\"brand\":null,\"model\":null,\"serial_number\":null,\"asset_category_id\":null,\"department_category_id\":null,\"industry_category_id\":null,\"asset_source\":null,\"asset_status\":null,\"asset_condition\":null,\"parent_id\":null,\"children_count\":0,\"region_code\":null,\"detailed_address\":null,\"start_date\":null,\"warranty_period\":null,\"warranty_alert\":null,\"maintenance_cycle\":null,\"expected_years\":null,\"related_entities\":null,\"remark\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":null,\"updated_at\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "创建资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets", "query": [], "raw": "{{baseUrl}}/api/admin/assets"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"办公台式电脑\",\"brand\":\"联想\",\"model\":\"ThinkCentre M720\",\"serial_number\":\"ABC123456789\",\"asset_category_ids\":[1,2,3],\"asset_source\":\"purchase\",\"asset_status\":\"new_unstocked\",\"asset_condition\":\"brand_new\",\"parent_id\":1,\"region_code\":\"12\",\"detailed_address\":\"XX街道XX号XX大厦\",\"start_date\":\"2024-01-01\",\"warranty_period\":36,\"warranty_alert\":30,\"maintenance_cycle\":90,\"expected_years\":5,\"related_entities\":[{\"entity_type\":\"manufacturer\",\"entity_id\":1,\"contact_name\":\"张三\",\"contact_phone\":\"13800138000\",\"position\":\"产品经理\",\"department\":\"产品部\"}],\"remark\":\"architecto\",\"attachments\":[1,2,3],\"is_accessory\":false}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取可作为主设备的资产列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/main-assets", "query": [{"key": "exclude_id", "value": "5", "description": "排除的资产ID（避免自己关联自己）", "disabled": false}, {"key": "keyword", "value": "%E7%94%B5%E8%84%91", "description": "搜索关键词", "disabled": false}, {"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页条数", "disabled": false}], "raw": "{{baseUrl}}/api/admin/assets/main-assets?exclude_id=5&keyword=%E7%94%B5%E8%84%91&page=1&per_page=20"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取资产详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/:asset_id", "query": [], "raw": "{{baseUrl}}/api/admin/assets/:asset_id", "variable": [{"id": "asset_id", "key": "asset_id", "value": "16", "description": "The ID of the asset."}, {"id": "asset", "key": "asset", "value": "1", "description": "资产ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "更新资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/:asset_id", "query": [], "raw": "{{baseUrl}}/api/admin/assets/:asset_id", "variable": [{"id": "asset_id", "key": "asset_id", "value": "16", "description": "The ID of the asset."}, {"id": "asset", "key": "asset", "value": "1", "description": "资产ID"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"办公台式电脑\",\"brand\":\"联想\",\"model\":\"ThinkCentre M720\",\"serial_number\":\"ABC123456789\",\"asset_category_ids\":[1,2,3],\"asset_source\":\"purchase\",\"asset_status\":\"in_use\",\"asset_condition\":\"brand_new\",\"parent_id\":1,\"region_code\":\"12\",\"detailed_address\":\"XX街道XX号XX大厦\",\"start_date\":\"2024-01-01\",\"warranty_period\":36,\"warranty_alert\":30,\"maintenance_cycle\":90,\"expected_years\":5,\"related_entities\":[{\"entity_type\":\"manufacturer\",\"entity_id\":1,\"contact_name\":\"张三\",\"contact_phone\":\"13800138000\",\"position\":\"产品经理\",\"department\":\"产品部\"}],\"remark\":\"architecto\",\"attachments\":[1,2,3],\"is_accessory\":false}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除资产", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/assets/:asset_id", "query": [], "raw": "{{baseUrl}}/api/admin/assets/:asset_id", "variable": [{"id": "asset_id", "key": "asset_id", "value": "16", "description": "The ID of the asset."}, {"id": "asset", "key": "asset", "value": "1", "description": "资产ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}, {"name": "附件管理", "description": "附件的上传、下载、删除等操作", "item": [{"name": "获取附件列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments", "query": [{"key": "page", "value": "1", "description": "页码", "disabled": false}, {"key": "per_page", "value": "20", "description": "每页数量", "disabled": false}, {"key": "file_name", "value": "example.pdf", "description": "文件名（模糊搜索）", "disabled": false}, {"key": "start_time", "value": "2024-01-01", "description": "开始时间", "disabled": false}, {"key": "end_time", "value": "2024-12-31", "description": "结束时间", "disabled": false}], "raw": "{{baseUrl}}/api/admin/attachments?page=1&per_page=20&file_name=example.pdf&start_time=2024-01-01&end_time=2024-12-31"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [], "code": 200, "body": "{\"data\":[{\"id\":\"\",\"file_name\":null,\"file_path\":null,\"file_size\":null,\"mime_type\":null,\"storage_type\":null,\"md5_hash\":null,\"file_url\":\"\",\"formatted_file_size\":\" bytes\",\"created_at\":null,\"updated_at\":null,\"attachable_type\":null,\"attachable_id\":null,\"category\":null,\"description\":null},{\"id\":\"\",\"file_name\":null,\"file_path\":null,\"file_size\":null,\"mime_type\":null,\"storage_type\":null,\"md5_hash\":null,\"file_url\":\"\",\"formatted_file_size\":\" bytes\",\"created_at\":null,\"updated_at\":null,\"attachable_type\":null,\"attachable_id\":null,\"category\":null,\"description\":null}],\"links\":{\"first\":\"\\/?page=1\",\"last\":\"\\/?page=1\",\"prev\":null,\"next\":null},\"meta\":{\"current_page\":1,\"from\":1,\"last_page\":1,\"links\":[{\"url\":null,\"label\":\"&laquo; Previous\",\"active\":false},{\"url\":\"\\/?page=1\",\"label\":\"1\",\"active\":true},{\"url\":null,\"label\":\"Next &raquo;\",\"active\":false}],\"path\":\"\\/\",\"per_page\":20,\"to\":2,\"total\":2}}", "name": ""}]}, {"name": "上传附件（本地上传）", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/upload", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/upload"}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "src": [], "type": "file"}]}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取附件详情", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/:attachment", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/:attachment", "variable": [{"id": "attachment", "key": "attachment", "value": "16", "description": "The attachment."}, {"id": "id", "key": "id", "value": "16", "description": "附件ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "下载附件", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/:attachment/download", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/:attachment/download", "variable": [{"id": "attachment", "key": "attachment", "value": "16", "description": "The attachment."}, {"id": "id", "key": "id", "value": "16", "description": "附件ID"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "删除附件", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/:attachment", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/:attachment", "variable": [{"id": "attachment", "key": "attachment", "value": "16", "description": "The attachment."}, {"id": "id", "key": "id", "value": "16", "description": "附件ID"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "获取OSS上传签名", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/oss/signature", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/oss/signature"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"filename\":\"example.pdf\",\"filesize\":1024000,\"mime_type\":\"application\\/pdf\",\"md5_hash\":\"5d41402abc4b2a76b9719d911017c592\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "OSS上传回调", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/oss/callback", "query": [], "raw": "{{baseUrl}}/api/admin/attachments/oss/callback"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"upload_id\":\"550e8400-e29b-41d4-a716-446655440000\",\"object_key\":\"attachments\\/2024\\/01\\/01\\/xxx.pdf\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "根据业务ID获取附件列表", "request": {"url": {"host": "{{baseUrl}}", "path": "api/admin/attachments/by-business", "query": [{"key": "attachable_type", "value": "App%5CModels%5CEntity", "description": "业务类型", "disabled": false}, {"key": "attachable_id", "value": "1", "description": "业务ID", "disabled": false}, {"key": "category", "value": "contract", "description": "附件分类", "disabled": false}], "raw": "{{baseUrl}}/api/admin/attachments/by-business?attachable_type=App%5CModels%5CEntity&attachable_id=1&category=contract"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"attachable_type\":\"architecto\",\"attachable_id\":16,\"category\":\"architecto\"}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "Authorization", "type": "string"}]}}