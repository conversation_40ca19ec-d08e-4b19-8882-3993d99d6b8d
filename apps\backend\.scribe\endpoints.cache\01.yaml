## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 字典管理
description: |-

  系统字典配置管理接口
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/dictionary/categories
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取字典分类列表
      description: 获取所有字典分类，支持条件筛选
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      name:
        name: name
        description: 分类名称
        required: false
        example: 设备类型
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      code:
        name: code
        description: 分类编码
        required: false
        example: device_type
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_enabled:
        name: is_enabled
        description: 是否启用
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      name: 设备类型
      code: device_type
      is_enabled: true
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/dictionary/categories
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 创建字典分类
      description: 创建新的字典分类
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: '分类编码，只能包含字母、数字和下划线，且不能以数字开头. Must match the regex /^[a-zA-Z_][a-zA-Z0-9_]*$/. Must not be greater than 50 characters.'
        required: true
        example: device_type
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      name:
        name: name
        description: '分类名称. Must not be greater than 100 characters.'
        required: true
        example: 设备类型
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: '分类描述. Must not be greater than 500 characters.'
        required: false
        example: 设备类型分类
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      sort:
        name: sort
        description: '排序值，数值越小越靠前. Must be at least 0.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_enabled:
        name: is_enabled
        description: 是否启用.
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: device_type
      name: 设备类型
      description: 设备类型分类
      sort: 1
      is_enabled: false
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/dictionary/categories/{category_id}'
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 更新字典分类
      description: 更新指定的字典分类信息
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      category_id:
        name: category_id
        description: 'The ID of the category.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: 字典分类ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      category_id: 1
      category: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: '分类编码，只能包含字母、数字和下划线，且不能以数字开头. Must match the regex /^[a-zA-Z_][a-zA-Z0-9_]*$/. Must not be greater than 50 characters.'
        required: true
        example: device_type
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      name:
        name: name
        description: '分类名称. Must not be greater than 100 characters.'
        required: true
        example: 设备类型
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: '分类描述. Must not be greater than 500 characters.'
        required: false
        example: 设备类型分类
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      sort:
        name: sort
        description: '排序值，数值越小越靠前. Must be at least 0.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_enabled:
        name: is_enabled
        description: 是否启用.
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: device_type
      name: 设备类型
      description: 设备类型分类
      sort: 1
      is_enabled: false
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/dictionary/categories/{category_id}'
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 删除字典分类
      description: 删除指定的字典分类（分类下存在字典项时无法删除）
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      category_id:
        name: category_id
        description: 'The ID of the category.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: 字典分类ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      category_id: 1
      category: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/admin/dictionary/items
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 获取字典项列表
      description: 获取所有字典项，支持条件筛选
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      category_id:
        name: category_id
        description: 分类ID
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      code:
        name: code
        description: 字典编码
        required: false
        example: desktop
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      value:
        name: value
        description: 字典值
        required: false
        example: 台式机
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_enabled:
        name: is_enabled
        description: 是否启用
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      category_id: 1
      code: desktop
      value: 台式机
      is_enabled: true
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/dictionary/items
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 创建字典项
      description: 在指定分类下创建新的字典项
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      category_id:
        name: category_id
        description: '所属字典分类ID. The <code>id</code> of an existing record in the dictionary_categories table.'
        required: true
        example: 1
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: '字典编码，同一分类下唯一. Must not be greater than 50 characters.'
        required: true
        example: desktop
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      value:
        name: value
        description: '字典值，用于显示. Must not be greater than 200 characters.'
        required: true
        example: 台式机
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      label:
        name: label
        description: '显示标签，可选的备用显示文本. Must not be greater than 200 characters.'
        required: false
        example: 台式计算机
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      sort:
        name: sort
        description: '排序值，数值越小越靠前. Must be at least 0.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      color:
        name: color
        description: '颜色值，格式为#开头的6位16进制. Must not be greater than 50 characters.'
        required: false
        example: '#FF5733'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      icon:
        name: icon
        description: '图标标识. Must not be greater than 50 characters.'
        required: false
        example: el-icon-monitor
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      config:
        name: config
        description: 扩展配置，JSON格式.
        required: false
        example:
          key: value
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      remark:
        name: remark
        description: '备注说明. Must not be greater than 500 characters.'
        required: false
        example: 用于标识台式计算机类型
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_enabled:
        name: is_enabled
        description: 是否启用.
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      category_id: 1
      code: desktop
      value: 台式机
      label: 台式计算机
      sort: 1
      color: '#FF5733'
      icon: el-icon-monitor
      config:
        key: value
      remark: 用于标识台式计算机类型
      is_enabled: false
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/dictionary/items/{item_id}'
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 更新字典项
      description: 更新指定的字典项信息
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      item_id:
        name: item_id
        description: 'The ID of the item.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      item:
        name: item
        description: 字典项ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      item_id: 1
      item: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      category_id:
        name: category_id
        description: '所属字典分类ID. The <code>id</code> of an existing record in the dictionary_categories table.'
        required: true
        example: 1
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      code:
        name: code
        description: '字典编码，同一分类下唯一. Must not be greater than 50 characters.'
        required: true
        example: desktop
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      value:
        name: value
        description: '字典值，用于显示. Must not be greater than 200 characters.'
        required: true
        example: 台式机
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      label:
        name: label
        description: '显示标签，可选的备用显示文本. Must not be greater than 200 characters.'
        required: false
        example: 台式计算机
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      sort:
        name: sort
        description: '排序值，数值越小越靠前. Must be at least 0.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      color:
        name: color
        description: '颜色值，格式为#开头的6位16进制. Must not be greater than 50 characters.'
        required: false
        example: '#FF5733'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      icon:
        name: icon
        description: '图标标识. Must not be greater than 50 characters.'
        required: false
        example: el-icon-monitor
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      config:
        name: config
        description: 扩展配置，JSON格式.
        required: false
        example:
          key: value
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      remark:
        name: remark
        description: '备注说明. Must not be greater than 500 characters.'
        required: false
        example: 用于标识台式计算机类型
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      is_enabled:
        name: is_enabled
        description: 是否启用.
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      category_id: 1
      code: desktop
      value: 台式机
      label: 台式计算机
      sort: 1
      color: '#FF5733'
      icon: el-icon-monitor
      config:
        key: value
      remark: 用于标识台式计算机类型
      is_enabled: false
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/dictionary/items/{item_id}'
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 删除字典项
      description: 删除指定的字典项
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      item_id:
        name: item_id
        description: 'The ID of the item.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      item:
        name: item
        description: 字典项ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      item_id: 1
      item: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/dictionary/code/{categoryCode}'
    metadata:
      groupName: 字典管理
      groupDescription: |-

        系统字典配置管理接口
      subgroup: ''
      subgroupDescription: ''
      title: 根据分类编码获取字典项
      description: 获取指定分类编码下的所有启用的字典项
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      categoryCode:
        name: categoryCode
        description: 分类编码
        required: true
        example: device_type
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      categoryCode: device_type
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"message":"Unauthenticated."}'
        headers:
          cache-control: 'no-cache, private'
          content-type: application/json
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
