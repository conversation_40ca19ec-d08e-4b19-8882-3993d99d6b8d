<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assets', function (Blueprint $table) {
            $table->id();

            // 基础信息
            $table->string('name', 200)->comment('资产名称');
            $table->string('brand', 100)->nullable()->comment('资产品牌');
            $table->string('model', 100)->nullable()->comment('规格型号');
            $table->string('serial_number', 100)->nullable()->comment('序列号');

            // 分类关联 (关联categories表)
            $table->json('asset_category_ids')->nullable()->comment('资产分类ID');

            // 字典字段
            $table->string('asset_source', 50)->nullable()->comment('资产来源');
            $table->string('asset_status', 50)->nullable()->comment('资产状态');
            $table->string('asset_condition', 50)->nullable()->comment('成色');

            // 父子关系
            $table->unsignedBigInteger('parent_id')->nullable()->comment('主设备ID');

            // 地址信息
            $table->string('region_code', 12)->nullable()->comment('区县代码');
            $table->text('detailed_address')->nullable()->comment('详细地址');

            // 时间和数值字段
            $table->date('start_date')->nullable()->comment('启用日期');
            $table->integer('warranty_period')->nullable()->comment('合同质保期(月)');
            $table->integer('warranty_alert')->nullable()->comment('质保期预警(天)');
            $table->integer('maintenance_cycle')->nullable()->comment('维护周期(天)');
            $table->integer('expected_years')->nullable()->comment('预计使用年限(年)');

            // JSON字段
            $table->json('related_entities')->nullable()->comment('相关主体信息');

            // 其他字段
            $table->text('remark')->nullable()->comment('备注');

            // 审计字段
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建人');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('更新人');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index(['asset_category_id'], 'idx_assets_category');
            $table->index(['department_category_id'], 'idx_assets_department');
            $table->index(['industry_category_id'], 'idx_assets_industry');
            $table->index(['parent_id'], 'idx_assets_parent');
            $table->index(['asset_status'], 'idx_assets_status');
            $table->index(['created_by'], 'idx_assets_created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assets');
    }
};
