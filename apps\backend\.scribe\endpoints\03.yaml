name: 用户管理
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/admin/users
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取用户列表
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      keyword:
        name: keyword
        description: 搜索关键词（用户名、手机号、邮箱）
        required: false
        example: admin
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 用户状态（enable/disable）
        required: false
        example: enable
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 页码
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 每页条数
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      keyword: admin
      status: enable
      page: 1
      per_page: 20
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"data":[{"id":1,"tenant_id":1,"username":"admin","email":null,"phone":null,"avatar":null,"avatar_id":null,"status":"enable","status_label":"\u542f\u7528","roles":[{"id":1,"name":"admin","description":"\u7cfb\u7edf\u7ba1\u7406\u5458"}],"created_at":"2025-07-24 09:20:45","updated_at":"2025-07-24 09:20:45"},{"id":1,"tenant_id":1,"username":"admin","email":null,"phone":null,"avatar":null,"avatar_id":null,"status":"enable","status_label":"\u542f\u7528","roles":[{"id":1,"name":"admin","description":"\u7cfb\u7edf\u7ba1\u7406\u5458"}],"created_at":"2025-07-24 09:20:45","updated_at":"2025-07-24 09:20:45"}],"links":{"first":"\/?page=1","last":"\/?page=1","prev":null,"next":null},"meta":{"current_page":1,"from":1,"last_page":1,"links":[{"url":null,"label":"&laquo; Previous","active":false},{"url":"\/?page=1","label":"1","active":true},{"url":null,"label":"Next &raquo;","active":false}],"path":"\/","per_page":20,"to":2,"total":2}}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/admin/users
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 创建用户
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      username:
        name: username
        description: 用户名
        required: true
        example: testuser
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email:
        name: email
        description: 邮箱
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      phone:
        name: phone
        description: 手机号
        required: false
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      status:
        name: status
        description: 状态（enable/disable）
        required: false
        example: enable
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      avatar_id:
        name: avatar_id
        description: 'The <code>id</code> of an existing record in the attachments table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      password:
        name: password
        description: 密码（最少6位）
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      username: testuser
      email: <EMAIL>
      phone: '13800138000'
      status: enable
      avatar_id: 16
      password: password123
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "data": {
              "id": 1,
              "username": "testuser",
              "email": "<EMAIL>"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/admin/users/{id}'
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 获取用户详情
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 用户ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      user: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "data": {
              "id": 1,
              "username": "admin",
              "email": "<EMAIL>"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/admin/users/{id}'
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 更新用户
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 用户ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      user: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      username:
        name: username
        description: 用户名
        required: false
        example: testuser
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email:
        name: email
        description: 邮箱
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      phone:
        name: phone
        description: 手机号
        required: false
        example: '13800138000'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      status:
        name: status
        description: 状态（enable/disable）
        required: false
        example: enable
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      avatar_id:
        name: avatar_id
        description: 'The <code>id</code> of an existing record in the attachments table.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      password:
        name: password
        description: 密码（为空则不更新）
        required: false
        example: newpassword
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
    cleanBodyParameters:
      username: testuser
      email: <EMAIL>
      phone: '13800138000'
      status: enable
      avatar_id: 16
      password: newpassword
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "data": {
              "id": 1,
              "username": "testuser",
              "email": "<EMAIL>"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/users/{id}'
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 删除用户
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the user.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 用户ID
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 1
      user: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 204
        content: '{}'
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/admin/users/{user_id}/roles/assign'
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 为用户分配角色（追加式）
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user_id:
        name: user_id
        description: 'The ID of the user.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 用户ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user_id: 1
      user: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      role_ids:
        name: role_ids
        description: 角色ID数组.
        required: true
        example:
          - 1
          - 2
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      role_ids:
        - 1
        - 2
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "id": 1,
            "name": "张三",
            "email": "<EMAIL>",
            "roles": [
              {
                "id": 1,
                "name": "管理员",
                "description": "系统管理员角色"
              },
              {
                "id": 2,
                "name": "普通用户",
                "description": "普通用户角色"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/admin/users/{user_id}/roles/sync'
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 同步用户角色（覆盖式）
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user_id:
        name: user_id
        description: 'The ID of the user.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 用户ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user_id: 1
      user: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      role_ids:
        name: role_ids
        description: 角色ID数组（将完全替换用户当前的角色）.
        required: true
        example:
          - 1
          - 2
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      role_ids:
        - 1
        - 2
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "id": 1,
            "name": "张三",
            "email": "<EMAIL>",
            "roles": [
              {
                "id": 1,
                "name": "管理员",
                "description": "系统管理员角色"
              },
              {
                "id": 2,
                "name": "普通用户",
                "description": "普通用户角色"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/admin/users/{user_id}/roles/remove'
    metadata:
      groupName: 用户管理
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 移除用户角色
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {YOUR_AUTH_KEY}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user_id:
        name: user_id
        description: 'The ID of the user.'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: 用户ID.
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      user_id: 1
      user: 1
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      role_ids:
        name: role_ids
        description: 要移除的角色ID数组.
        required: true
        example:
          - 1
          - 2
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      role_ids:
        - 1
        - 2
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "id": 1,
            "name": "张三",
            "email": "<EMAIL>",
            "roles": [
              {
                "id": 1,
                "name": "管理员",
                "description": "系统管理员角色"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth:
      - headers
      - Authorization
      - 'Bearer 6g43cv8PD1aE5beadkZfhV6'
    controller: null
    method: null
    route: null
    custom: []
